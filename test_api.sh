#!/bin/bash

# 拍照搜题API测试脚本
# 使用方法: ./test_api.sh

BASE_URL="http://localhost:8080"
PHONE="13800138000"
PASSWORD="123456"
INVITE_CODE="SOLVE2024"

echo "=== 拍照搜题API测试脚本 ==="
echo "Base URL: $BASE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "请求: $method $url"
    if [ -n "$data" ]; then
        echo "数据: $data"
    fi
    echo ""
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$url")
    else
        response=$(curl -s -X $method "$BASE_URL$url")
    fi
    
    echo -e "${YELLOW}响应:${NC}"
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 检查服务是否启动
echo -e "${BLUE}1. 检查服务健康状态${NC}"
health_response=$(curl -s "$BASE_URL/health")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 服务正常运行${NC}"
    echo "$health_response" | python3 -m json.tool 2>/dev/null || echo "$health_response"
else
    echo -e "${RED}✗ 服务未启动或无法访问${NC}"
    echo "请确保服务已启动并监听在 $BASE_URL"
    exit 1
fi
echo ""
echo "----------------------------------------"
echo ""

# 测试发送验证码
test_api "POST" "/api/v1/user/send-code" \
    "{\"phone\":\"$PHONE\"}" \
    "发送验证码"

# 等待用户输入验证码
echo -e "${YELLOW}请查看控制台输出的验证码，然后输入:${NC}"
read -p "验证码: " CODE

# 测试用户注册
test_api "POST" "/api/v1/user/register" \
    "{\"phone\":\"$PHONE\",\"password\":\"$PASSWORD\",\"code\":\"$CODE\",\"invite_code\":\"$INVITE_CODE\"}" \
    "用户注册"

# 测试用户登录
test_api "POST" "/api/v1/user/login" \
    "{\"phone\":\"$PHONE\",\"password\":\"$PASSWORD\"}" \
    "用户登录"

# 测试获取用户信息（假设用户ID为1）
test_api "GET" "/api/v1/user/profile/1" \
    "" \
    "获取用户信息"

# 测试更新用户信息
test_api "PUT" "/api/v1/user/profile/1" \
    "{\"password\":\"newpassword123\"}" \
    "更新用户密码"

# 测试用新密码登录
test_api "POST" "/api/v1/user/login" \
    "{\"phone\":\"$PHONE\",\"password\":\"newpassword123\"}" \
    "使用新密码登录"

# 测试错误情况
echo -e "${BLUE}=== 错误情况测试 ===${NC}"
echo ""

# 测试重复注册
test_api "POST" "/api/v1/user/register" \
    "{\"phone\":\"$PHONE\",\"password\":\"$PASSWORD\",\"code\":\"123456\",\"invite_code\":\"$INVITE_CODE\"}" \
    "重复注册（应该失败）"

# 测试错误的手机号格式
test_api "POST" "/api/v1/user/send-code" \
    "{\"phone\":\"1380013800\"}" \
    "错误的手机号格式（应该失败）"

# 测试错误的密码
test_api "POST" "/api/v1/user/login" \
    "{\"phone\":\"$PHONE\",\"password\":\"wrongpassword\"}" \
    "错误的密码（应该失败）"

# 测试不存在的用户
test_api "GET" "/api/v1/user/profile/999" \
    "" \
    "获取不存在的用户信息（应该失败）"

# 测试错误的邀请码
test_api "POST" "/api/v1/user/register" \
    "{\"phone\":\"13900139000\",\"password\":\"$PASSWORD\",\"code\":\"123456\",\"invite_code\":\"WRONG\"}" \
    "错误的邀请码（应该失败）"

echo -e "${GREEN}=== 测试完成 ===${NC}"
echo ""
echo "注意事项："
echo "1. 确保MySQL和Redis服务已启动"
echo "2. 确保数据库配置正确"
echo "3. 验证码在控制台输出，实际环境中会发送短信"
echo "4. 某些测试可能因为数据已存在而失败，这是正常的"
