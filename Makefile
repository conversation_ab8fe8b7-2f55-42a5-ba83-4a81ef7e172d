# 拍照搜题API项目Makefile

.PHONY: help build run test clean docker-build docker-run docker-stop init-db

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  build        - 构建应用程序"
	@echo "  run          - 运行应用程序"
	@echo "  test         - 运行测试"
	@echo "  test-api     - 运行API测试"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 安装依赖"
	@echo "  fmt          - 格式化代码"
	@echo "  lint         - 代码检查"

	@echo "  init-db      - 初始化数据库"

# 构建应用程序
build:
	@echo "构建应用程序..."
	go build -o main cmd/main.go

# 运行应用程序（开发环境）
run:
	@echo "启动应用程序（开发环境）..."
	go run cmd/main.go

# 运行应用程序（生产环境）
run-prod:
	@echo "启动应用程序（生产环境）..."
	go run cmd/main.go -config config/config.prod.yaml

# 运行测试
test:
	@echo "运行测试..."
	go test -v ./...

# 运行API测试
test-api:
	@echo "运行API测试..."
	./test_api.sh

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -f main
	rm -rf logs/*

# 安装依赖
deps:
	@echo "安装依赖..."
	go mod tidy
	go mod download

# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
lint:
	@echo "代码检查..."
	golangci-lint run



# 初始化数据库
init-db:
	@echo "初始化数据库..."
	mysql -h localhost -u root -p < scripts/init_db.sql

# 开发环境设置
dev-setup: deps
	@echo "设置开发环境..."
	mkdir -p logs
	@echo "开发环境设置完成"

# 生产环境构建
prod-build:
	@echo "生产环境构建..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o main cmd/main.go

# 运行代码覆盖率测试
coverage:
	@echo "运行代码覆盖率测试..."
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 安全检查
security:
	@echo "运行安全检查..."
	gosec ./...

# 性能测试
benchmark:
	@echo "运行性能测试..."
	go test -bench=. -benchmem ./...

# 更新依赖
update-deps:
	@echo "更新依赖..."
	go get -u ./...
	go mod tidy

# 检查代码质量
quality: fmt lint security
	@echo "代码质量检查完成"

# 完整测试流程
full-test: quality test coverage
	@echo "完整测试流程完成"

# 本地开发启动
dev: dev-setup run

# 生产部署
deploy: prod-build
	@echo "生产部署准备完成"
