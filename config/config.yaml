server:
  port: 8080
  mode: debug # debug, release, test

database:
  mysql:
    host: ***********
    port: 3306
    username: gmdns
    password: "5e7fFn3HpPfuQ6Qx42Az"
    database: go_solve
    charset: utf8mb4
    parse_time: true
    loc: Local
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

redis:
  host: ************
  port: 6379
  password: "y4HY8xm8dECYmDSeaX8GC"
  db: 0
  pool_size: 10
  min_idle_conns: 5

log:
  level: info # debug, info, warn, error
  filename: logs/app.log
  max_size: 100 # MB
  max_age: 30   # days
  max_backups: 10
  compress: true

app:
  name: solve_api
  version: 1.0.0
  invite_code: "SOLVE2024" # 默认邀请码
  max_apps_per_user: 5     # 每个用户最多创建的应用数量
  rate_limit: 10           # 默认限流：10次/秒
  cache_ttl: 604800        # Redis缓存TTL：7天（秒）

sms:
  # 阿里云短信服务配置
  provider: aliyun
  access_key_id: "LTAI5t9g26wqutn692bcCRmR"
  access_key_secret: "******************************"
  sign_name: "青岛果沐云"
  template_code: "SMS_294081777"
  region: "cn-hangzhou"

models:
  # 模型服务配置
  qwen_vl_plus:
    api_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
    api_key: ""
    timeout: 30
  deepseek_chat:
    api_url: "https://api.deepseek.com/v1/chat/completions"
    api_key: "***********************************"
    timeout: 30
