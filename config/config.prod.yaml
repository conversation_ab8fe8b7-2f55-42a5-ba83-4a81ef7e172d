server:
  port: 8080
  mode: release

database:
  mysql:
    host: ***********
    port: 3380
    username: gmdns
    password: "5e7fFn3HpPfuQ6Qx42Az"
    database: go_solve
    charset: utf8mb4
    parse_time: true
    loc: Local
    max_idle_conns: 20
    max_open_conns: 200
    conn_max_lifetime: 3600

redis:
  host: ************
  port: 6379
  password: "y4HY8xm8dECYmDSeaX8GC"
  db: 0
  pool_size: 20
  min_idle_conns: 10

log:
  level: info
  filename: logs/app.log
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true

app:
  name: solve_api
  version: 1.0.0
  invite_code: "SOLVE2024"
  max_apps_per_user: 5
  rate_limit: 100
  cache_ttl: 604800

sms:
  provider: aliyun
  access_key_id: "LTAI5t9g26wqutn692bcCRmR"
  access_key_secret: "******************************"
  sign_name: "青岛果沐云"
  template_code: "SMS_294081777"
  region: "cn-hangzhou"

models:
  qwen_vl_plus:
    api_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
    api_key: ""
    timeout: 30
  deepseek_chat:
    api_url: "https://api.deepseek.com/v1/chat/completions"
    api_key: "***********************************"
    timeout: 30
