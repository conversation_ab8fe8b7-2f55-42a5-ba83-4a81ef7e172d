version: '3.8'

services:
  # API服务
  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SOLVE_API_DATABASE_MYSQL_HOST=mysql
      - SOLVE_API_DATABASE_MYSQL_PASSWORD=solve_api_password
      - SOLVE_API_REDIS_HOST=redis
      # 生产环境请使用实际配置
      - SOLVE_API_SMS_PROVIDER=mock
      - SOLVE_API_MODELS_DEEPSEEK_CHAT_API_KEY=${DEEPSEEK_API_KEY:-}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - solve_api_network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: solve_api_password
      MYSQL_DATABASE: solve_api
      MYSQL_USER: solve_api_user
      MYSQL_PASSWORD: solve_api_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - solve_api_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - solve_api_network

  # 可选：Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - solve_api_network

  # 可选：MySQL管理界面
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: solve_api_password
    ports:
      - "8082:80"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - solve_api_network

volumes:
  mysql_data:
  redis_data:

networks:
  solve_api_network:
    driver: bridge
