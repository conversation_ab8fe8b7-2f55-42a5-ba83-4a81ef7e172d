# 拍照搜题 API 服务

基于 Golang 开发的拍照搜题 Web API 服务，采用模块化设计，包含用户管理、应用管理、鉴权流控、模型服务等核心功能模块。

## 项目概述

本项目旨在提供一款高性能、可扩展的拍照搜题API服务，支持用户注册、应用管理、题目搜索等功能。

### 技术栈

- **语言**: Golang 1.21+
- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0+
- **ORM**: GORM
- **日志**: zap + lumberjack
- **配置管理**: viper

## 项目结构

```
├── cmd/                  # 应用程序入口
│   └── main.go
├── config/               # 配置文件
│   └── config.yaml
├── internal/             # 私有应用和库代码
│   ├── api/              # API 层
│   ├── middleware/       # 中间件
│   ├── model/            # 数据模型
│   ├── repository/       # 数据访问层
│   ├── service/          # 业务逻辑层
│   ├── utils/            # 工具函数
│   ├── config/           # 配置管理
│   └── database/         # 数据库连接
├── pkg/                  # 可以被外部应用使用的库代码
├── scripts/              # 构建、安装、分析等脚本
├── test/                 # 测试文件
├── logs/                 # 日志文件目录
├── API_DOCS.md          # API接口文档
├── test_api.sh          # API测试脚本
└── README.md            # 项目说明
```

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

### 安装依赖

```bash
go mod tidy
```

### 配置服务

1. **数据库配置**：项目已配置远程MySQL和Redis服务器
2. **短信服务**：已集成阿里云短信服务
3. **AI模型**：已配置DeepSeek API密钥

### 环境配置

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 根据需要修改 `.env` 文件中的配置

3. 或者直接使用配置文件：
   - 开发环境：`config/config.yaml`
   - 生产环境：`config/config.prod.yaml`

### 启动服务

```bash
go run cmd/main.go
```

服务将在 `http://localhost:8080` 启动。

### 验证服务

访问健康检查接口：

```bash
curl http://localhost:8080/health
```

## 当前功能

### 已实现功能（3.1 用户注册与账户模块）

- ✅ 用户注册（手机号 + 验证码 + 密码 + 邀请码）
- ✅ 用户登录（手机号 + 密码）
- ✅ 获取用户信息
- ✅ 更新用户信息
- ✅ 发送验证码（模拟）
- ✅ 系统配置管理
- ✅ 数据库自动迁移
- ✅ 统一响应格式
- ✅ 参数验证
- ✅ 错误处理
- ✅ 日志记录

### API接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 用户注册 | POST | `/api/v1/user/register` | 用户注册 |
| 用户登录 | POST | `/api/v1/user/login` | 用户登录 |
| 获取用户信息 | GET | `/api/v1/user/profile/{user_id}` | 获取用户信息 |
| 更新用户信息 | PUT | `/api/v1/user/profile/{user_id}` | 更新用户信息 |
| 发送验证码 | POST | `/api/v1/user/send-code` | 发送验证码 |
| 健康检查 | GET | `/health` | 服务健康检查 |

详细的API文档请查看 [API_DOCS.md](./API_DOCS.md)

## 测试

### 自动化测试

运行测试脚本：

```bash
./test_api.sh
```

### 手动测试

1. 发送验证码：

```bash
curl -X POST http://localhost:8080/api/v1/user/send-code \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

2. 用户注册：

```bash
curl -X POST http://localhost:8080/api/v1/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone":"13800138000",
    "password":"123456",
    "code":"123456",
    "invite_code":"SOLVE2024"
  }'
```

3. 用户登录：

```bash
curl -X POST http://localhost:8080/api/v1/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone":"13800138000",
    "password":"123456"
  }'
```

## 配置说明

### 主要配置项

- `server.port`: 服务端口（默认8080）
- `database.mysql`: MySQL数据库配置
- `redis`: Redis缓存配置
- `app.invite_code`: 注册邀请码（默认SOLVE2024）
- `app.rate_limit`: API限流配置（默认10次/秒）
- `log`: 日志配置

### 环境变量

支持通过环境变量覆盖配置，环境变量前缀为 `SOLVE_API_`：

```bash
export SOLVE_API_DATABASE_MYSQL_PASSWORD=your_password
export SOLVE_API_REDIS_PASSWORD=your_redis_password
```

## 开发计划

### 下一步开发（按优先级）

1. **应用管理模块** - 用户可创建和管理应用
2. **鉴权与流控模块** - API密钥认证和限流
3. **模型服务配置模块** - 配置AI模型参数
4. **拍照搜题业务模块** - 核心业务功能
5. **计费与账户扣费模块** - 用户余额和计费
6. **日志记录与统计分析模块** - 数据统计和分析
7. **系统配置与后台管理模块** - 管理员功能

### 技术改进

- [ ] 添加JWT认证
- [x] 实现真实的短信服务集成（已完成）
- [ ] 添加单元测试
- [ ] 添加API文档生成（Swagger）
- [x] 添加Docker支持（已完成）
- [ ] 性能优化和监控

## 注意事项

1. **短信服务**: 已集成阿里云短信服务，验证码会发送到手机
2. **邀请码**: 默认为"SOLVE2024"，可通过配置修改
3. **密码安全**: 使用bcrypt加密存储
4. **数据库**: 使用远程MySQL和Redis服务器
5. **日志**: 日志文件保存在 `logs/` 目录下
6. **AI模型**: 已配置DeepSeek API密钥

## 相关文档

- [API接口文档](./API_DOCS.md)
- [短信服务接入文档](./SMS_INTEGRATION_DOCS.md)
- [项目开发总结](./PROJECT_SUMMARY.md)

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目采用MIT许可证。
