我计划使用golang开发一款web的API服务。

# 框架与语言约定
- golang
- gin框架
- redis
- mysql 8 
可能还需要其他的库或者组建，需要你帮我分析功能逻辑后在视情况添加。


# 要求；
1. 纯服务端开发，不考虑任何web界面的开发。web端将由其他团队进行。只需要丢出相关接口即可。
2. 高热度开发，在开发过程中尽可能给我更高级的方案建议。但不能直接执行，需要先跟我确认后在进行。
3. 先处理结构化框架模型。梳理整体业务逻辑。然后在进行模块化的开发。模块化开发前需要先跟我对接讨论更细节的需求。


# 核心模块划分一览
1.用户注册与账户模块
2.应用管理模块
3.鉴权与流控模块
4.模型服务配置模块
5.拍照搜题（type = 1）业务处理模块
6.数据缓存与存储模块
7.日志记录与统计分析模块
8.计费与账户扣费模块
9.系统配置与后台管理模块（接口式）
10.接口设计与交互规范

1. 用户注册与账户模块
功能需求：
通过手机号 + 验证码 + 密码 + 邀请码完成注册；
邀请码作为注册口令，存储于 config 配置表中，可由管理员修改；
用户账号信息需唯一，手机号不可重复；
成功注册后初始化账户余额字段（默认值为 0）；

2. 应用管理模块
功能需求：
每个用户最多可创建 N 个应用，创建时填写名称并选择业务类型；
自动生成 app_key / secret_key 作为调用凭证；
应用状态支持：正常、冻结；
冻结后不可调用 API；secret_key 可重置；
用户可修改应用名称，不能删除或变更 type；


3. 鉴权与流控模块
功能需求：
请求携带 app_key 与 secret_key 进行校验；
根据 app_key 识别绑定业务类型（type）；
校验请求频率（默认 10 次/秒）；
校验用户余额是否足够执行当前业务；
校验参数合法性（如 type=1 需 img_url）；

4. 模型服务配置模块
功能需求：
系统支持配置多个模型的调用参数（如 prompt、top_p 等）；
支持动态加载与在线修改；
模型名称作为主键，如 qwen-vl-plus / deepseek-chat；

5. 拍照搜题业务（type = 1）
功能需求流程：
校验 img_url 是否有效；
调用 qwen-vl-plus 获取题目结构；
对返回结构体构造缓存 key 查询 Redis；
Redis 未命中 → 查 MySQL；
MySQL 未命中 → 调用 deepseek-chat 继续补全；
格式化后写入 MySQL / Redis，返回结果给用户；


6. 数据缓存与持久化模块
功能需求：
Redis 用于结构化题目的缓存（key 为哈希构造）；
Redis 内容 TTL 默认 7 天；
MySQL 为最终数据归档与查询依据；

Redis Key 规则：
将qwen返回数据进行格式化解析后的内容建立缓存键。

7. 日志记录与统计分析模块
功能需求：
记录用户所有请求行为，包括成功/失败；
模型调用日志（是否命中缓存、调用时间、来源模型）；
请求次数统计 / 应用访问分布 / 用户使用趋势；

8. 计费与账户扣费模块
功能需求：
用户账户统一余额（所有 APP 共用）；
每次请求前需预检查余额；
成功才扣费，失败请求不计费；
支持系统默认价格 + 用户定制价格；

9. 系统配置与后台管理模块（接口）
功能需求：
模型参数配置接口（支持修改）
用户价格配置接口
应用冻结/恢复控制接口
系统邀请注册口令设置接口
所有接口需管理员权限