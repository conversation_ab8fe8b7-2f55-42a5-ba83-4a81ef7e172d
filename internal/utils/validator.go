package utils

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// 自定义验证器
var customValidator *validator.Validate

func init() {
	// 获取gin默认的验证器
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		customValidator = v
		// 注册自定义验证规则
		v.RegisterValidation("phone", validatePhone)
	}
}

// validatePhone 验证手机号
func validatePhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	return ValidatePhone(phone)
}

// GetValidationErrors 获取验证错误信息
func GetValidationErrors(err error) []string {
	var errors []string
	
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			errors = append(errors, getErrorMessage(e))
		}
	} else {
		errors = append(errors, err.Error())
	}
	
	return errors
}

// getErrorMessage 根据验证错误生成友好的错误信息
func getErrorMessage(e validator.FieldError) string {
	field := getFieldName(e)
	
	switch e.Tag() {
	case "required":
		return fmt.Sprintf("%s不能为空", field)
	case "min":
		return fmt.Sprintf("%s长度不能少于%s位", field, e.Param())
	case "max":
		return fmt.Sprintf("%s长度不能超过%s位", field, e.Param())
	case "len":
		return fmt.Sprintf("%s长度必须为%s位", field, e.Param())
	case "email":
		return fmt.Sprintf("%s格式不正确", field)
	case "phone":
		return fmt.Sprintf("%s格式不正确", field)
	case "numeric":
		return fmt.Sprintf("%s必须为数字", field)
	case "alpha":
		return fmt.Sprintf("%s只能包含字母", field)
	case "alphanum":
		return fmt.Sprintf("%s只能包含字母和数字", field)
	default:
		return fmt.Sprintf("%s验证失败", field)
	}
}

// getFieldName 获取字段的中文名称
func getFieldName(e validator.FieldError) string {
	fieldName := e.Field()
	
	// 字段名映射
	fieldMap := map[string]string{
		"Phone":      "手机号",
		"Password":   "密码",
		"Code":       "验证码",
		"InviteCode": "邀请码",
		"Name":       "名称",
		"Type":       "类型",
		"Status":     "状态",
		"Balance":    "余额",
		"Amount":     "金额",
		"AppKey":     "应用密钥",
		"SecretKey":  "密钥",
	}
	
	if chineseName, exists := fieldMap[fieldName]; exists {
		return chineseName
	}
	
	return fieldName
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	return customValidator.Struct(s)
}

// FormatValidationError 格式化验证错误为字符串
func FormatValidationError(err error) string {
	errors := GetValidationErrors(err)
	return strings.Join(errors, "; ")
}
