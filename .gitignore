# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
logs/
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Config files with sensitive data
config/config.local.yaml
config/config.prod.yaml

# Build output
dist/
build/
main

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Coverage reports
coverage.txt
coverage.html

# Air live reload
tmp/

# Docker volumes
mysql_data/
redis_data/
